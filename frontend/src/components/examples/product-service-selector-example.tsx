import { Component, State, h, Listen } from '@stencil/core';
import {
  ProductTypes,
  ServiceTypes,
  ProductIndustries,
  ServiceIndustries,
} from '../../global/script/var';

/**
 * Example component demonstrating usage of the new global variables
 * for product/service types and industries in survey creation
 */
@Component({
  tag: 'product-service-selector-example',
  styleUrl: 'product-service-selector-example.css',
  shadow: true,
})
export class ProductServiceSelectorExample {
  @State() isProduct: boolean = true;
  @State() selectedType: string = '';
  @State() selectedIndustry: string = '';

  @Listen('selectChangeEvent')
  handleSelectChange(event: CustomEvent) {
    const { name, value } = event.detail;
    
    if (name === 'productOrService') {
      this.isProduct = value === 'product';
      // Reset selections when switching between product/service
      this.selectedType = '';
      this.selectedIndustry = '';
    } else if (name === 'typeSelect') {
      this.selectedType = value;
    } else if (name === 'industrySelect') {
      this.selectedIndustry = value;
    }
  }

  render() {
    return (
      <div class="product-service-selector">
        <h2>Product/Service Selector Example</h2>
        <p>This demonstrates how to use the new global variables for survey targeting.</p>
        
        <l-spacer value={2}></l-spacer>
        
        {/* Step 1: Product or Service */}
        <e-text><strong>Are you testing for a product or service?</strong></e-text>
        <l-spacer value={0.5}></l-spacer>
        <l-row justifyContent="flex-start">
          <e-input
            type="radio"
            name="productOrService"
            value="product"
            checked={this.isProduct}
          >
            Product
          </e-input>
          <l-spacer variant="horizontal" value={1}></l-spacer>
          <e-input
            type="radio"
            name="productOrService"
            value="service"
            checked={!this.isProduct}
          >
            Service
          </e-input>
        </l-row>
        
        <l-spacer value={2}></l-spacer>
        
        {/* Step 2: Type Selection */}
        <e-text>
          <strong>What type of {this.isProduct ? 'product' : 'service'} is it?</strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-select
          name="typeSelect"
          options={JSON.stringify(this.isProduct ? ProductTypes : ServiceTypes)}
        ></e-select>
        
        <l-spacer value={2}></l-spacer>
        
        {/* Step 3: Industry Selection */}
        <e-text>
          <strong>What industry does the {this.isProduct ? 'product' : 'service'} fall in?</strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-select
          name="industrySelect"
          options={JSON.stringify(this.isProduct ? ProductIndustries : ServiceIndustries)}
        ></e-select>
        
        <l-spacer value={3}></l-spacer>
        
        {/* Display Selected Values */}
        {(this.selectedType || this.selectedIndustry) && (
          <div class="selection-summary">
            <e-text><strong>Current Selection:</strong></e-text>
            <l-spacer value={0.5}></l-spacer>
            <e-text>Type: {this.isProduct ? 'Product' : 'Service'}</e-text>
            {this.selectedType && this.selectedType !== '-' && (
              <e-text>Category: {this.selectedType}</e-text>
            )}
            {this.selectedIndustry && this.selectedIndustry !== '-' && (
              <e-text>Industry: {this.selectedIndustry}</e-text>
            )}
          </div>
        )}
      </div>
    );
  }
}
